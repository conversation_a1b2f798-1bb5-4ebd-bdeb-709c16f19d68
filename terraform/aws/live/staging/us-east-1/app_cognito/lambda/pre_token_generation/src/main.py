import json


def lambda_handler(event, context):
    """
    Cognito Pre Token Generation Lambda function for logging purposes.
    
    This function logs user authentication events and token generation details
    without modifying the token claims.
    
    AWS Documentation: https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-lambda-pre-token-generation.html#aws-lambda-triggers-pre-token-generation-example-version-2-overview
    """
    
    try:
        # Remove aws.cognito.signin.user.admin from the scopes so the user can't call cognito APIs directly
        event['response'] = {
            'claimsAndScopeOverrideDetails': {
                'accessTokenGeneration': {
                    'scopesToSuppress': [
                        'aws.cognito.signin.user.admin'
                    ]
                }
            }
        }
        return event
    except Exception as e:
        print(f"Error in pre-token generation function", extra={
            'error': str(e),
            'event': event,
        })
        return event
