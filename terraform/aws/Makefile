.PHONY: clean-lock-cache

clean-lock-cache:
	@if [ -z "$(env)" ]; then \
		echo "Error: env parameter is required. Usage: make clean-lock-cache env=staging|prod"; \
		exit 1; \
	fi
	@if [ "$(env)" != "staging" ] && [ "$(env)" != "prod" ]; then \
		echo "Error: env must be 'staging' or 'prod'"; \
		exit 1; \
	fi
	@echo "Cleaning .terraform.lock.hcl files in $(env) environment..."
	@find ./$(env) -name ".terraform.lock.hcl" -type f -delete 2>/dev/null || true
	@echo "Cleaning .terragrunt-cache directories in $(env) environment..."
	@find ./$(env) -name ".terragrunt-cache" -type d -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleanup completed for $(env) environment"
